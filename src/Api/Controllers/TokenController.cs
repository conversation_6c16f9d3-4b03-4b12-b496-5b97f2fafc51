using HighCapital.AuthenticationService.Application.Helpers;
using HighCapital.AuthenticationService.Domain.Dtos;
using Microsoft.AspNetCore.Mvc;

namespace HighCapital.AuthenticationService.Api.Controllers;

[ApiController]
[Route("[controller]")]
public class TokenController : ControllerBase
{
    private readonly ILogger<TokenController> _logger;

    public TokenController(ILogger<TokenController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Verifies the validity of a JWT token
    /// </summary>
    /// <param name="request">Token verification request containing the JWT token</param>
    /// <returns>Token verification response with validity status and claims</returns>
    [HttpPost("verify")]
    public Task<ActionResult<string>> VerifyToken()
    {
        var token = Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (string.IsNullOrWhiteSpace(token))
        {
            return Forbid("Not authorized");
        }
        _logger.LogInformation("Verifying token: {Token}", token);
        return Ok("Valid token");
    }
}
