using HighCapital.AuthenticationService.Api.Helpers;
using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Services;
using HighCapital.Core.Dependencies;
using HighCapital.AuthenticationService.Infrastructure.ExternalServices;
using Microsoft.AspNetCore.Mvc;


namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddWebServices(this IHostApplicationBuilder builder)
    {
        //services
        builder.Services.AddScoped<IUserService, UserService>();
        
        //google
        var googleClientId = builder.Configuration["GoogleOAuth:ClientId"];
        var googleClientSecret = builder.Configuration["GoogleOAuth:ClientSecret"];
        var googleRedirectUri = builder.Configuration["GoogleOAuth:RedirectUri"];

        //jwt - já configurado no Infrastructure layer


        //doc
        builder.Services.AddOpenApiDocumentation();

        builder.Services.AddSingleton(new GoogleOAuthService(
            googleClientId!,
            googleClientSecret!,
            googleRedirectUri!
        ));

        // DbContext já configurado no Infrastructure layer

        builder.Services.AddHttpContextAccessor();
        builder.Services.AddExceptionHandler<CustomExceptionHandler>();
        builder.Services.AddProblemDetails();

        // Add Health Checks
        builder.Services.AddHealthChecks();

        // Customise default API behaviour
        builder.Services.Configure<ApiBehaviorOptions>(options =>
            options.SuppressModelStateInvalidFilter = true);

        builder.Services.AddEndpointsApiExplorer();

    }
}
