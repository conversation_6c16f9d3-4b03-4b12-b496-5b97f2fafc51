﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>HighCapital.AuthenticationService.Application</RootNamespace>
    <AssemblyName>HighCapital.AuthenticationService.Application</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="Google.Apis.Auth" />
    <PackageReference Include="HighCapital.Core" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

</Project>
