
using System.Text.Json;
using System.Text.Json.Serialization;

namespace HighCapital.AuthenticationService.Infrastructure.ExternalServices;

public class GoogleOAuthService
{
    private readonly string _clientId;
    private readonly string _clientSecret;
    private readonly string _redirectUri;
    private readonly HttpClient _httpClient;

    public GoogleOAuthService(string clientId, string clientSecret, string redirectUri)
    {
        _clientId = clientId;
        _clientSecret = clientSecret;
        _redirectUri = redirectUri;
        _httpClient = new HttpClient();
    }

    public async Task<GoogleTokenResponse> ExchangeCodeForTokensAsync(string code)
    {
        var values = new Dictionary<string, string>
    {
        { "code", code },
        { "client_id", _clientId },
        { "client_secret", _clientSecret },
        { "redirect_uri", _redirectUri },
        { "grant_type", "authorization_code" }
    };

        var content = new FormUrlEncodedContent(values);

        var response = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", content);
        var json = await response.Content.ReadAsStringAsync();

       
        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Erro do Google OAuth: {response.StatusCode} - {json}");
        }

        var tokenResponse = JsonSerializer.Deserialize<GoogleTokenResponse>(json,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

        return tokenResponse!;
    }
}


public class GoogleTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = null!;

    [JsonPropertyName("id_token")]
    public string IdToken { get; set; } = null!;

    [JsonPropertyName("refresh_token")]
    public string? RefreshToken { get; set; }

    [JsonPropertyName("scope")]
    public string Scope { get; set; } = null!;

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = null!;

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }
}