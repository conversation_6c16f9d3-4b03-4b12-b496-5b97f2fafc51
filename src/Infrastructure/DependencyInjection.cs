using HighCapital.AuthenticationService.Domain.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using HighCapital.Core.Infrastructure.Database;
using HighCapital.Core.Dependencies;
using Microsoft.EntityFrameworkCore;

namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddInfrastructureServices(this IHostApplicationBuilder builder)
    {
        // Configure Core DbContext (for business entities)
        builder.Services.AddDbContext<CoreDbContext>(options =>
            options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));


        //jwt
        builder.Services.AddJWTAuthentication();
        
        builder.Services.AddAuthorization(options =>
            options.AddPolicy(Policies.CanPurge, policy => policy.RequireRole(Roles.Administrator)));
    }
}
