name: Auto Format Code

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to format (default: current branch)'
        required: false
        default: ''
  issue_comment:
    types: [created]

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  auto-format:
    name: Auto Format Code
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'workflow_dispatch' || 
      (github.event_name == 'issue_comment' && 
       github.event.issue.pull_request && 
       contains(github.event.comment.body, '/format'))
    
    permissions:
      contents: write
      pull-requests: write

    steps:
    - name: Get branch name
      id: branch
      run: |
        if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
          BRANCH="${{ github.event.inputs.branch }}"
          if [ -z "$BRANCH" ]; then
            BRANCH="${{ github.ref_name }}"
          fi
        else
          # Get PR branch from comment
          PR_NUMBER="${{ github.event.issue.number }}"
          BRANCH=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER" \
            | jq -r '.head.ref')
        fi
        echo "branch=$BRANCH" >> $GITHUB_OUTPUT
        echo "Formatting branch: $BRANCH"

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ steps.branch.outputs.branch }}
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore

    - name: Check current formatting
      id: check-format
      run: |
        if dotnet format --verify-no-changes --verbosity diagnostic; then
          echo "needs-formatting=false" >> $GITHUB_OUTPUT
          echo "✅ Code is already properly formatted"
        else
          echo "needs-formatting=true" >> $GITHUB_OUTPUT
          echo "❌ Code needs formatting"
        fi

    - name: Apply formatting
      if: steps.check-format.outputs.needs-formatting == 'true'
      run: |
        echo "🎨 Applying code formatting..."
        dotnet format --verbosity diagnostic
        
        # Check what changed
        if git diff --quiet; then
          echo "No changes after formatting"
          echo "changes-made=false" >> $GITHUB_OUTPUT
        else
          echo "Formatting changes applied"
          echo "changes-made=true" >> $GITHUB_OUTPUT
          
          # Show what changed
          echo "Files modified:"
          git diff --name-only
        fi

    - name: Commit changes
      if: steps.check-format.outputs.needs-formatting == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        if ! git diff --quiet; then
          git add .
          git commit -m "🎨 Auto-format code with dotnet format
          
          - Applied .NET code formatting rules
          - Fixed whitespace and indentation issues
          - No functional changes made
          
          Co-authored-by: github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>"
          
          git push origin ${{ steps.branch.outputs.branch }}
          echo "✅ Changes committed and pushed"
        else
          echo "ℹ️ No changes to commit"
        fi

    - name: Comment on PR (if from comment trigger)
      if: github.event_name == 'issue_comment' && steps.check-format.outputs.needs-formatting == 'true'
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const needsFormatting = '${{ steps.check-format.outputs.needs-formatting }}' === 'true';
          
          if (needsFormatting) {
            const body = `🎨 **Auto-formatting completed!**
            
            I've applied \`dotnet format\` to fix code formatting issues in this PR.
            
            **Changes made:**
            - Fixed whitespace and indentation
            - Applied .NET formatting rules
            - No functional changes
            
            The code should now pass the formatting checks! ✨`;
            
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: body
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: '✅ Code is already properly formatted! No changes needed.'
            });
          }

    - name: Summary
      if: always()
      run: |
        echo "## 🎨 Auto-Format Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ steps.check-format.outputs.needs-formatting }}" == "true" ]; then
          echo "✅ **Formatting Applied**: Code has been automatically formatted" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Actions taken:**" >> $GITHUB_STEP_SUMMARY
          echo "- Applied \`dotnet format\`" >> $GITHUB_STEP_SUMMARY
          echo "- Committed changes to branch: \`${{ steps.branch.outputs.branch }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- Fixed whitespace and indentation issues" >> $GITHUB_STEP_SUMMARY
        else
          echo "ℹ️ **No Changes Needed**: Code is already properly formatted" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Branch**: \`${{ steps.branch.outputs.branch }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Trigger**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
