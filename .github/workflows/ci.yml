name: CI - Build and Test

on:
  pull_request:
    branches:
      - main
      - release
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '9.0.x'
  PROJECT_ID: highcapital-470117
  IMAGE: authentication-service

jobs:
  build-and-test:
    name: Build, Test and Validate
    runs-on: ubuntu-latest

    permissions:
      contents: read

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration Release --no-restore

    - name: Run unit tests
      id: domain-tests
      continue-on-error: true
      run: |
        dotnet test tests/Domain.UnitTests/Domain.UnitTests.csproj \
          --configuration Release \
          --no-build \
          --verbosity normal \
          --logger trx \
          --results-directory TestResults/ \
          --logger "console;verbosity=detailed"

    - name: Run application tests
      id: app-tests
      continue-on-error: true
      run: |
        dotnet test tests/Application.UnitTests/Application.UnitTests.csproj \
          --configuration Release \
          --no-build \
          --verbosity normal \
          --logger trx \
          --results-directory TestResults/ \
          --logger "console;verbosity=detailed"
    - name: Build Docker image (validation)
      id: docker-build
      continue-on-error: true
      run: |
        docker build -t $IMAGE:pr-${{ github.event.number || 'latest' }} .

    - name: Test Docker image
      id: docker-test
      continue-on-error: true
      if: steps.docker-build.outcome == 'success'
      run: |
        # Start container in background
        docker run -d --name test-container -p 8080:8080 $IMAGE:pr-${{ github.event.number || 'latest' }}

        # Wait for container to be ready
        sleep 30

        # Test if container is responding
        if curl -f http://localhost:8080/swagger; then
          echo "✅ Container is responding correctly"
          DOCKER_STATUS="success"
        else
          echo "❌ Container is not responding"
          DOCKER_STATUS="failure"
        fi

        # Stop and remove container
        docker stop test-container || true
        docker rm test-container || true

        # Set output for summary
        echo "status=$DOCKER_STATUS" >> $GITHUB_OUTPUT

    - name: Generate CI Summary
      if: always()
      run: |
        echo "## 🔍 CI Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        # Overall status
        TESTS_FAILED="${{ steps.test-results.outputs.tests-failed }}"
        DOCKER_BUILD_STATUS="${{ steps.docker-build.outcome }}"
        DOCKER_TEST_STATUS="${{ steps.docker-test.outputs.status }}"

        if [ "$TESTS_FAILED" == "true" ]; then
          echo "❌ **Overall Status**: TESTS FAILED" >> $GITHUB_STEP_SUMMARY
        elif [ "$DOCKER_BUILD_STATUS" == "failure" ]; then
          echo "❌ **Overall Status**: DOCKER BUILD FAILED" >> $GITHUB_STEP_SUMMARY
        elif [ "$DOCKER_TEST_STATUS" == "failure" ]; then
          echo "❌ **Overall Status**: DOCKER TEST FAILED" >> $GITHUB_STEP_SUMMARY
        else
          echo "✅ **Overall Status**: ALL CHECKS PASSED" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Detailed Results:**" >> $GITHUB_STEP_SUMMARY

        # Build status
        echo "- ✅ Code compilation: SUCCESS" >> $GITHUB_STEP_SUMMARY

        # Test results
        DOMAIN_STATUS="${{ steps.domain-tests.outcome }}"
        APP_STATUS="${{ steps.app-tests.outcome }}"

        if [ "$DOMAIN_STATUS" == "success" ]; then
          echo "- ✅ Domain tests: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "- ❌ Domain tests: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        if [ "$APP_STATUS" == "success" ]; then
          echo "- ✅ Application tests: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "- ❌ Application tests: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        # Docker results
        if [ "$DOCKER_BUILD_STATUS" == "success" ]; then
          echo "- ✅ Docker image build: SUCCESS" >> $GITHUB_STEP_SUMMARY
        else
          echo "- ❌ Docker image build: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        if [ "$DOCKER_TEST_STATUS" == "success" ]; then
          echo "- ✅ Container validation: SUCCESS" >> $GITHUB_STEP_SUMMARY
        elif [ "$DOCKER_TEST_STATUS" == "failure" ]; then
          echo "- ❌ Container validation: FAILED" >> $GITHUB_STEP_SUMMARY
        else
          echo "- ⚠️ Container validation: SKIPPED (build failed)" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Commit**: \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
        echo "**Workflow**: [View Details](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY

        # Set final job status
        if [ "$TESTS_FAILED" == "true" ]; then
          echo "❌ CI failed due to test failures"
          exit 1
        fi

    - name: Set job status output
      if: always()
      id: status
      run: |
        echo "status=${{ job.status }}" >> $GITHUB_OUTPUT
        echo "emoji=${{ job.status == 'success' && '✅' || '❌' }}" >> $GITHUB_OUTPUT
