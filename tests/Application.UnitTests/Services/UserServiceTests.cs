using FluentAssertions;
using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Common.Models;
using HighCapital.AuthenticationService.Application.Services;
using HighCapital.AuthenticationService.Domain.Dtos;
using HighCapital.AuthenticationService.Infrastructure.ExternalServices;
using HighCapital.Core.Domain.Entities;
using HighCapital.Core.Infrastructure.Database;
using HighCapital.Core.Infrastructure.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;

namespace HighCapital.AuthenticationService.Application.UnitTests.Services;


[TestFixture]
public class UserServiceTests
{
    private Mock<CoreDbContext> _mockContext;
    private Mock<DbSet<User>> _mockUserSet;
    private Mock<IIdentityService> _mockIdentityService;
    private Mock<ILogger<UserService>> _mockLogger;
    private UserService _userService;
    private Mock<UserRepository> _userRepository;

    [SetUp]
    public void SetUp()
    {
        _mockContext = new Mock<CoreDbContext>();
        _mockUserSet = new Mock<DbSet<User>>();
        _userRepository = new Mock<UserRepository>();
        _mockIdentityService = new Mock<IIdentityService>();
        _mockLogger = new Mock<ILogger<UserService>>();

        _mockContext.Setup(x => x.Users).Returns(_mockUserSet.Object);

        _userService = new UserService(
            _mockContext.Object,
            _userRepository.Object,
            _mockLogger.Object
            );
    }

    [Test]
    public async Task RegisterUserAsync_WithValidData_ShouldReturnResult()
    {
        // Arrange
        var user = new CreateUserDto
        {
            Name = "Jonh",
            Email     = "<EMAIL>",
            Password  = "password123",
            Phone  = "22999999999"
        };


        // Act
        var result = await _userService.RegisterUserAsync(user);

        // Assert - Testing that method returns a Result (success or failure)
        result.Should().NotBeNull();
        result.Should().BeOfType<Result<int>>();
    }

    [Test]
    public async Task LoginUserAsync_WithValidData_ShouldReturnResult()
    {
        // Arrange
        var user = new LoginDto
        {
            Email     = "<EMAIL>",
            Password  = "password123"
        };;

        // Act
        var result = await _userService.LoginUserAsync(user);

        // Assert - Testing that method returns a Result (success or failure)
        result.Should().NotBeNull();
        result.Should().BeOfType<Result<string>>();
    }

    [Test]
    public async Task GetUserByIdAsync_WithValidId_ShouldReturnResult()
    {
        // Arrange
        const int userId = 1;

        // Act
        var result = await _userService.GetUserByIdAsync(userId);

        // Assert - Testing that method returns a Result (success or failure)
        result.Should().NotBeNull();
        result.Should().BeOfType<Result<User>>();
    }

    [Test]
    public async Task DeleteUserAsync_WithValidId_ShouldReturnResult()
    {
        // Arrange
        const int userId = 1;

        // Act
        var result = await _userService.DeleteUserAsync(userId);

        // Assert - Testing that method returns a Result (success or failure)
        result.Should().NotBeNull();
        result.Should().BeOfType<Result>();
    }

    [Test]
    public async Task GetAllUsersAsync_ShouldReturnResult()
    {
        // Act
        var result = await _userService.GetAllUsersAsync();

        // Assert - Testing that method returns a Result (success or failure)
        result.Should().NotBeNull();
        result.Should().BeOfType<Result<IEnumerable<User>>>();
    }
}
